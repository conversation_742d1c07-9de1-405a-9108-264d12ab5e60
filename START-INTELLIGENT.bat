@echo off
chcp 65001 >nul
title 🧠 AI Chat Bot - Intelligent Engine

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                🧠 AI Chat Bot - المحرك الذكي المستقل 🧠                   ║
echo ║                        يعمل بدون الاعتماد على خدمات خارجية                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: تحديد المسار الحالي
set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

echo 🧠 تهيئة المحرك الذكي المستقل...
echo.

echo ✨ المميزات الجديدة:
echo    🚀 محرك ذكاء اصطناعي مدمج ومتقدم
echo    🧠 5 نماذج متخصصة (عام، برمجة، إبداع، علوم، أعمال)
echo    💡 استجابات ذكية بدون اتصال إنترنت
echo    🎯 تحليل عاطفي متقدم
echo    📚 قاعدة معرفة شاملة
echo    🔄 تعلم من التفاعلات
echo.

echo 🔍 فحص المتطلبات الأساسية...

:: فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر: 
node --version

:: فحص npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر: 
npm --version

:: فحص التبعيات
if not exist "node_modules" (
    echo ⚠️ التبعيات غير مثبتة، جاري التثبيت...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات!
        pause
        exit /b 1
    )
)

echo ✅ التبعيات متوفرة

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                           🧠 المحرك الذكي المدمج                          ║
echo ║                                                                              ║
echo ║  🎯 النماذج المتخصصة المتاحة:                                              ║
echo ║  • 🧠 المساعد الذكي العام - لجميع المواضيع                               ║
echo ║  • 💻 خبير البرمجة - متخصص في تطوير البرمجيات                           ║
echo ║  • 🎨 المبدع الأدبي - للكتابة الإبداعية والأدب                           ║
echo ║  • 🔬 العالم المتخصص - للعلوم والرياضيات                                ║
echo ║  • 💼 مستشار الأعمال - للإدارة والتسويق                                  ║
echo ║                                                                              ║
echo ║  ✨ المميزات المتقدمة:                                                     ║
echo ║  • يعمل بدون اتصال إنترنت                                                 ║
echo ║  • استجابات فورية وذكية                                                   ║
echo ║  • تحليل عاطفي للرسائل                                                    ║
echo ║  • قاعدة معرفة شاملة                                                       ║
echo ║  • تعلم من التفاعلات                                                       ║
echo ║  • دعم كامل للغة العربية                                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 بدء تشغيل المحرك الذكي...
echo 📝 ملاحظة: لا حاجة لأي خدمات خارجية - كل شيء مدمج!
echo 🌐 سيتم فتح التطبيق في المتصفح على: http://localhost:5173
echo.

:: تشغيل التطبيق
start "AI Chat Bot - Intelligent Engine" cmd /k "npm run dev"

:: انتظار قليل ثم فتح المتصفح
echo ⏳ انتظار بدء المحرك الذكي...
timeout /t 8 /nobreak >nul

echo 🌐 فتح التطبيق في المتصفح...
start http://localhost:5173

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        🎉 المحرك الذكي جاهز للعمل! 🎉                     ║
echo ║                                                                              ║
echo ║  🔗 رابط التطبيق: http://localhost:5173                                    ║
echo ║  🧠 المحرك: نشط ومستقل                                                    ║
echo ║  ⚡ الاستجابة: فورية                                                        ║
echo ║  🔒 الخصوصية: كاملة (لا يرسل بيانات خارجية)                             ║
echo ║                                                                              ║
echo ║  💡 نصائح للاستخدام:                                                       ║
echo ║  • جرب النماذج المختلفة من القائمة العلوية                               ║
echo ║  • استخدم مكتبة القوالب 📚 للحصول على أفضل النتائج                     ║
echo ║  • النظام يتعلم من تفاعلاتك ويحسن الاستجابات                            ║
echo ║  • يمكنك العمل بدون اتصال إنترنت بالكامل                                ║
echo ║                                                                              ║
echo ║  🎯 أمثلة للتجربة:                                                          ║
echo ║  • "اشرح لي React بطريقة مبسطة"                                          ║
echo ║  • "اكتب لي قصة قصيرة عن المستقبل"                                       ║
echo ║  • "ما هي أفضل استراتيجيات التسويق؟"                                     ║
echo ║  • "اشرح لي قانون نيوتن الثاني"                                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🎉 استمتع بتجربة الذكاء الاصطناعي المستقل!
echo 💡 لا حاجة لـ Msty أو LM Studio - كل شيء مدمج ومتقدم!
echo.
pause
