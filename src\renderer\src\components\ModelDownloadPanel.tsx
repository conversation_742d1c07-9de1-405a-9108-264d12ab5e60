import React, { useState, useEffect } from 'react'
import { X, Download, ExternalLink, Info, CheckCircle, AlertCircle } from 'lucide-react'

interface ModelInfo {
  id: string
  name: string
  description: string
  size: string
  downloadCommand: string
  available: boolean
  provider: string
}

interface ModelDownloadPanelProps {
  isOpen: boolean
  onClose: () => void
  availableModels: ModelInfo[]
  onRefresh: () => void
}

const ModelDownloadPanel: React.FC<ModelDownloadPanelProps> = ({
  isOpen,
  onClose,
  availableModels,
  onRefresh
}) => {
  const [selectedProvider, setSelectedProvider] = useState<string>('ollama')
  const [searchTerm, setSearchTerm] = useState('')
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(false)

  // تصفية النماذج
  const filteredModels = availableModels.filter(model => {
    const matchesProvider = selectedProvider === 'all' || model.provider === selectedProvider
    const matchesSearch = model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         model.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAvailability = !showOnlyAvailable || model.available
    
    return matchesProvider && matchesSearch && matchesAvailability
  })

  // نسخ الأمر للحافظة
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // يمكن إضافة إشعار هنا
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              📥 تحميل النماذج مفتوحة المصدر
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              اختر وحمل النماذج المناسبة لاحتياجاتك
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-4 items-center">
            {/* Provider Filter */}
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                المزود:
              </label>
              <select
                value={selectedProvider}
                onChange={(e) => setSelectedProvider(e.target.value)}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              >
                <option value="all">جميع المزودين</option>
                <option value="ollama">🦙 Ollama</option>
                <option value="huggingface">🤗 HuggingFace</option>
              </select>
            </div>

            {/* Search */}
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                البحث:
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="ابحث عن نموذج..."
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm w-48"
              />
            </div>

            {/* Available Only */}
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={showOnlyAvailable}
                onChange={(e) => setShowOnlyAvailable(e.target.checked)}
                className="rounded"
              />
              <span className="text-gray-700 dark:text-gray-300">المحملة فقط</span>
            </label>

            {/* Refresh */}
            <button
              onClick={onRefresh}
              className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
            >
              🔄 تحديث
            </button>
          </div>
        </div>

        {/* Models List */}
        <div className="flex-1 overflow-y-auto p-6">
          {filteredModels.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                لا توجد نماذج تطابق المعايير المحددة
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredModels.map((model) => (
                <div
                  key={model.id}
                  className={`border rounded-lg p-4 transition-all ${
                    model.available
                      ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {model.name}
                        </h3>
                        {model.available && (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {model.description}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>📦 الحجم: {model.size}</span>
                        <span>🏷️ المزود: {model.provider}</span>
                        <span>🆔 المعرف: {model.id}</span>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      {model.available ? (
                        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 text-sm">
                          <CheckCircle className="w-4 h-4" />
                          <span>محمل</span>
                        </div>
                      ) : (
                        <div className="flex flex-col gap-2">
                          <button
                            onClick={() => copyToClipboard(model.downloadCommand)}
                            className="flex items-center gap-2 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
                          >
                            <Download className="w-4 h-4" />
                            نسخ الأمر
                          </button>
                          
                          <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 p-2 rounded font-mono">
                            {model.downloadCommand}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-start gap-4">
            <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p className="font-medium mb-2">💡 نصائح للتحميل:</p>
              <ul className="space-y-1 text-xs">
                <li>• تأكد من وجود مساحة كافية على القرص الصلب</li>
                <li>• النماذج الأكبر تحتاج ذاكرة أكثر للتشغيل</li>
                <li>• ابدأ بالنماذج الصغيرة مثل Phi-3 Mini إذا كان جهازك محدود</li>
                <li>• Llama 3.1 8B خيار ممتاز للاستخدام العام</li>
              </ul>
              
              <div className="mt-3 flex items-center gap-4">
                <a
                  href="https://ollama.ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-blue-500 hover:text-blue-600"
                >
                  <ExternalLink className="w-3 h-3" />
                  موقع Ollama
                </a>
                <a
                  href="https://huggingface.co/models"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-blue-500 hover:text-blue-600"
                >
                  <ExternalLink className="w-3 h-3" />
                  مكتبة HuggingFace
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ModelDownloadPanel
