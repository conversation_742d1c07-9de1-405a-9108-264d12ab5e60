@echo off
chcp 65001 >nul
title 🚀 AI Chat Bot - Enhanced Edition

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🤖 AI Chat Bot - Enhanced Edition 🤖                     ║
echo ║                        مع دعم متقدم لـ Msty والخدمات المحلية                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: تحديد المسار الحالي
set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

echo 🔍 فحص متطلبات النظام...

:: فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
node --version

:: فحص npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر
npm --version

echo.
echo 🔍 فحص الخدمات المحلية المتاحة...

:: فحص Msty
echo 🟣 فحص Msty...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:10000/v1/models' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ Msty متصل على المنفذ 10000' } catch { Write-Host '⚠️ Msty غير متصل على المنفذ 10000' }"

:: فحص LM Studio
echo 🏠 فحص LM Studio...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:1234/v1/models' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ LM Studio متصل على المنفذ 1234' } catch { Write-Host '⚠️ LM Studio غير متصل على المنفذ 1234' }"

:: فحص Ollama
echo 🦙 فحص Ollama...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:11434/api/tags' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ Ollama متصل على المنفذ 11434' } catch { Write-Host '⚠️ Ollama غير متصل على المنفذ 11434' }"

echo.
echo 📦 فحص التبعيات...

:: فحص وجود node_modules
if not exist "node_modules" (
    echo ⚠️ التبعيات غير مثبتة، جاري التثبيت...
    echo 📥 تثبيت التبعيات...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات!
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ التبعيات متوفرة
)

echo.
echo 🔧 إعداد البيئة...

:: إنشاء ملف البيئة إذا لم يكن موجوداً
if not exist ".env" (
    echo 📝 إنشاء ملف البيئة...
    (
        echo # AI Chat Bot Environment Configuration
        echo OPENROUTER_API_KEY=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
        echo MSTY_BASE_URL=http://localhost:10000/v1
        echo OLLAMA_BASE_URL=http://localhost:11434
        echo LM_STUDIO_BASE_URL=http://localhost:1234/v1
        echo PREFERRED_PROVIDER=auto
        echo USE_EMOTIONAL_AI=true
        echo AUTO_SWITCH_MODELS=true
    ) > .env
    echo ✅ تم إنشاء ملف البيئة
)

echo.
echo 🚀 بدء تشغيل التطبيق...

:: اختيار وضع التشغيل
echo.
echo اختر وضع التشغيل:
echo [1] تطوير (Development) - مع إعادة التحميل التلقائي
echo [2] إنتاج (Production) - أداء محسن
echo [3] تشغيل سريع (Quick Start) - بدون بناء
echo.
set /p "mode=أدخل اختيارك (1-3): "

if "%mode%"=="1" (
    echo 🔄 تشغيل في وضع التطوير...
    echo 📝 ملاحظة: سيتم إعادة تحميل التطبيق تلقائياً عند تغيير الملفات
    start "AI Chat Bot - Dev Mode" cmd /k "npm run dev"
    goto :end
)

if "%mode%"=="2" (
    echo 🏗️ بناء التطبيق للإنتاج...
    call npm run build
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء التطبيق!
        pause
        exit /b 1
    )
    echo 🚀 تشغيل التطبيق...
    start "AI Chat Bot - Production" cmd /k "npm start"
    goto :end
)

if "%mode%"=="3" (
    echo ⚡ تشغيل سريع...
    start "AI Chat Bot - Quick Start" cmd /k "npm run dev:skip-ts"
    goto :end
)

echo ❌ اختيار غير صحيح!
pause
exit /b 1

:end
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              🎉 تم بدء التطبيق! 🎉                          ║
echo ║                                                                              ║
echo ║  💡 نصائح للاستخدام الأمثل:                                                  ║
echo ║  • تأكد من تشغيل Msty أو LM Studio للنماذج المحلية                         ║
echo ║  • استخدم مفتاح OpenRouter للنماذج السحابية المتقدمة                       ║
echo ║  • جرب مكتبة Prompts المتقدمة للحصول على أفضل النتائج                     ║
echo ║  • فعّل التحليل العاطفي للاستجابات الذكية                                  ║
echo ║                                                                              ║
echo ║  🔗 روابط مفيدة:                                                             ║
echo ║  • Msty: https://msty.app/                                                  ║
echo ║  • LM Studio: https://lmstudio.ai/                                          ║
echo ║  • Ollama: https://ollama.ai/                                               ║
echo ║  • OpenRouter: https://openrouter.ai/                                       ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: انتظار قليل ثم فتح المتصفح
timeout /t 3 /nobreak >nul
echo 🌐 فتح التطبيق في المتصفح...
start http://localhost:5173

echo.
echo ✨ استمتع بتجربة الذكاء الاصطناعي المتقدمة!
echo 📞 للدعم والمساعدة، راجع ملف README.md
echo.
pause
