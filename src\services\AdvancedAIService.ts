/**
 * خدمة الذكاء الاصطناعي المتقدمة
 * تجمع جميع الخدمات والمزودين في واجهة موحدة ومتطورة
 */

import { OpenRouterAPI, AIModel, ChatMessage } from './OpenRouterAPI'
import { MstyAPI } from './MstyAPI'
import { OllamaAPI } from './OllamaAPI'
import { promptsLibrary, PromptTemplate } from './PromptsLibrary'
import { emotionalAI } from './EmotionalAI'

export interface ServiceProvider {
  id: string
  name: string
  type: 'local' | 'cloud'
  status: 'connected' | 'disconnected' | 'error'
  models: AIModel[]
  capabilities: string[]
  priority: number
}

export interface ConversationContext {
  id: string
  title: string
  messages: ChatMessage[]
  currentModel: string
  provider: string
  settings: ConversationSettings
  metadata: {
    createdAt: string
    updatedAt: string
    totalTokens: number
    emotionalProfile: any
  }
}

export interface ConversationSettings {
  temperature: number
  maxTokens: number
  systemPrompt?: string
  useEmotionalAI: boolean
  autoSwitchModels: boolean
  preferredProvider: 'local' | 'cloud' | 'auto'
}

export class AdvancedAIService {
  private providers: Map<string, ServiceProvider> = new Map()
  private openRouterAPI: OpenRouterAPI | null = null
  private mstyAPI: MstyAPI
  private ollamaAPI: OllamaAPI
  private isInitialized = false
  private currentConversation: ConversationContext | null = null

  constructor() {
    this.mstyAPI = new MstyAPI()
    this.ollamaAPI = new OllamaAPI('http://localhost:11434/v1')
    this.initializeProviders()
  }

  // تهيئة جميع المزودين
  private async initializeProviders(): Promise<void> {
    console.log('🚀 تهيئة خدمة الذكاء الاصطناعي المتقدمة...')

    // تهيئة المزودين المحليين
    await this.initializeLocalProviders()
    
    // تهيئة المزودين السحابيين
    await this.initializeCloudProviders()

    this.isInitialized = true
    console.log('✅ تم تهيئة الخدمة بنجاح')
  }

  // تهيئة المزودين المحليين
  private async initializeLocalProviders(): Promise<void> {
    // Msty Provider
    try {
      const mstyAvailable = await this.mstyAPI.checkAvailability()
      const mstyModels = await this.mstyAPI.getModels()
      
      this.providers.set('msty', {
        id: 'msty',
        name: 'Msty (محلي)',
        type: 'local',
        status: mstyAvailable ? 'connected' : 'disconnected',
        models: mstyModels,
        capabilities: ['chat', 'streaming', 'local-privacy', 'custom-models'],
        priority: 1
      })
      
      console.log(`🟣 Msty: ${mstyAvailable ? 'متصل' : 'غير متصل'} (${mstyModels.length} نموذج)`)
    } catch (error) {
      console.error('❌ خطأ في تهيئة Msty:', error)
    }

    // Ollama Provider
    try {
      const ollamaAvailable = await this.ollamaAPI.checkAvailability()
      const ollamaModels = await this.ollamaAPI.getModels()
      
      this.providers.set('ollama', {
        id: 'ollama',
        name: 'Ollama (محلي)',
        type: 'local',
        status: ollamaAvailable ? 'connected' : 'disconnected',
        models: ollamaModels.map(model => ({
          id: model.id,
          name: model.name,
          description: model.description,
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 4096,
          architecture: { modality: 'text', tokenizer: 'local' },
          top_provider: { is_moderated: false }
        })),
        capabilities: ['chat', 'streaming', 'local-privacy', 'open-source'],
        priority: 2
      })
      
      console.log(`🦙 Ollama: ${ollamaAvailable ? 'متصل' : 'غير متصل'} (${ollamaModels.length} نموذج)`)
    } catch (error) {
      console.error('❌ خطأ في تهيئة Ollama:', error)
    }
  }

  // تهيئة المزودين السحابيين
  private async initializeCloudProviders(): Promise<void> {
    // OpenRouter Provider
    const apiKey = localStorage.getItem('openrouter-api-key') || 
                   '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'
    
    if (apiKey) {
      try {
        this.openRouterAPI = new OpenRouterAPI(apiKey)
        const connectionTest = await this.openRouterAPI.testConnection()
        const cloudModels = await this.openRouterAPI.getAvailableModels()
        
        this.providers.set('openrouter', {
          id: 'openrouter',
          name: 'OpenRouter (سحابي)',
          type: 'cloud',
          status: connectionTest ? 'connected' : 'disconnected',
          models: cloudModels,
          capabilities: ['chat', 'streaming', 'multiple-models', 'high-quality'],
          priority: 3
        })
        
        console.log(`🌐 OpenRouter: ${connectionTest ? 'متصل' : 'غير متصل'} (${cloudModels.length} نموذج)`)
      } catch (error) {
        console.error('❌ خطأ في تهيئة OpenRouter:', error)
      }
    }
  }

  // الحصول على جميع المزودين
  getProviders(): ServiceProvider[] {
    return Array.from(this.providers.values()).sort((a, b) => a.priority - b.priority)
  }

  // الحصول على المزودين المتصلين
  getConnectedProviders(): ServiceProvider[] {
    return this.getProviders().filter(provider => provider.status === 'connected')
  }

  // الحصول على جميع النماذج المتاحة
  getAllAvailableModels(): AIModel[] {
    const allModels: AIModel[] = []
    
    this.providers.forEach(provider => {
      if (provider.status === 'connected') {
        allModels.push(...provider.models)
      }
    })
    
    return allModels
  }

  // اختيار أفضل مزود للمهمة
  selectBestProvider(
    task: 'chat' | 'code' | 'creative' | 'analysis',
    preference: 'local' | 'cloud' | 'auto' = 'auto'
  ): ServiceProvider | null {
    const connectedProviders = this.getConnectedProviders()
    
    if (connectedProviders.length === 0) {
      return null
    }

    // إذا كان التفضيل محدد
    if (preference !== 'auto') {
      const preferredProviders = connectedProviders.filter(p => p.type === preference)
      if (preferredProviders.length > 0) {
        return preferredProviders[0] // أعلى أولوية
      }
    }

    // اختيار تلقائي بناءً على المهمة
    switch (task) {
      case 'code':
        // تفضيل المزودين المحليين للبرمجة (خصوصية)
        return connectedProviders.find(p => p.type === 'local') || connectedProviders[0]
      
      case 'creative':
        // تفضيل المزودين السحابيين للإبداع (جودة أعلى)
        return connectedProviders.find(p => p.type === 'cloud') || connectedProviders[0]
      
      case 'analysis':
        // تفضيل المزودين السحابيين للتحليل
        return connectedProviders.find(p => p.type === 'cloud') || connectedProviders[0]
      
      default:
        // للمحادثة العامة، اختيار أول مزود متاح
        return connectedProviders[0]
    }
  }

  // إرسال رسالة مع اختيار تلقائي للمزود والنموذج
  async sendMessage(
    message: string,
    options: {
      model?: string
      provider?: string
      usePromptTemplate?: string
      conversationId?: string
      settings?: Partial<ConversationSettings>
    } = {}
  ): Promise<{
    success: boolean
    response?: string
    provider?: string
    model?: string
    usage?: any
    error?: string
    emotionalAnalysis?: any
  }> {
    try {
      if (!this.isInitialized) {
        await this.initializeProviders()
      }

      // تحديد المحادثة الحالية أو إنشاء جديدة
      let conversation = this.currentConversation
      if (options.conversationId) {
        // تحميل المحادثة المحددة (يمكن تطويرها لاحقاً)
      }

      // معالجة قالب Prompt إذا تم تحديده
      let processedMessage = message
      if (options.usePromptTemplate) {
        const template = promptsLibrary.getPromptById(options.usePromptTemplate)
        if (template) {
          processedMessage = template.prompt + '\n\n' + message
          promptsLibrary.recordUsage(template.id)
        }
      }

      // تحليل المشاعر
      const emotionalAnalysis = emotionalAI.getFullEmotionalAnalysis(processedMessage, options.model || '')

      // اختيار المزود والنموذج
      const taskType = this.detectTaskType(processedMessage)
      const selectedProvider = options.provider ? 
        this.providers.get(options.provider) : 
        this.selectBestProvider(taskType, options.settings?.preferredProvider)

      if (!selectedProvider || selectedProvider.status !== 'connected') {
        throw new Error('لا يوجد مزود متاح للذكاء الاصطناعي')
      }

      // اختيار النموذج
      const selectedModel = options.model || this.selectBestModel(selectedProvider, taskType)

      // إعداد الرسائل
      const messages: ChatMessage[] = [
        {
          role: 'user',
          content: processedMessage
        }
      ]

      // إضافة سياق المحادثة إذا كان متاحاً
      if (conversation) {
        messages.unshift(...conversation.messages.slice(-10)) // آخر 10 رسائل
      }

      // إرسال الرسالة حسب المزود
      let result: any
      switch (selectedProvider.id) {
        case 'msty':
          result = await this.mstyAPI.sendMessage(messages, selectedModel)
          break
        case 'ollama':
          result = await this.ollamaAPI.sendMessage(processedMessage, selectedModel, messages.slice(0, -1))
          break
        case 'openrouter':
          if (this.openRouterAPI) {
            result = await this.openRouterAPI.sendMessage(processedMessage, selectedModel, messages.slice(0, -1))
          }
          break
        default:
          throw new Error(`مزود غير مدعوم: ${selectedProvider.id}`)
      }

      // معالجة النتيجة
      if (result && (result.success !== false)) {
        const response = typeof result === 'string' ? result : result.message || result
        
        return {
          success: true,
          response,
          provider: selectedProvider.id,
          model: selectedModel,
          usage: result.usage,
          emotionalAnalysis: emotionalAnalysis.emotionalState.confidence > 0.7 ? {
            emotion: emotionalAnalysis.emotionalState.primary,
            intensity: emotionalAnalysis.emotionalState._intensity,
            confidence: emotionalAnalysis.emotionalState.confidence
          } : undefined
        }
      } else {
        throw new Error(result?.error || 'فشل في الحصول على رد من الذكاء الاصطناعي')
      }

    } catch (error) {
      console.error('❌ خطأ في إرسال الرسالة:', error)
      return {
        success: false,
        error: (error as any).message || 'خطأ غير معروف'
      }
    }
  }

  // تحديد نوع المهمة من الرسالة
  private detectTaskType(message: string): 'chat' | 'code' | 'creative' | 'analysis' {
    const lowerMessage = message.toLowerCase()
    
    if (lowerMessage.includes('كود') || lowerMessage.includes('برمجة') || 
        lowerMessage.includes('code') || lowerMessage.includes('function')) {
      return 'code'
    }
    
    if (lowerMessage.includes('قصة') || lowerMessage.includes('شعر') || 
        lowerMessage.includes('إبداع') || lowerMessage.includes('creative')) {
      return 'creative'
    }
    
    if (lowerMessage.includes('تحليل') || lowerMessage.includes('بيانات') || 
        lowerMessage.includes('analysis') || lowerMessage.includes('data')) {
      return 'analysis'
    }
    
    return 'chat'
  }

  // اختيار أفضل نموذج للمهمة
  private selectBestModel(provider: ServiceProvider, taskType: string): string {
    const models = provider.models
    
    if (models.length === 0) {
      throw new Error('لا توجد نماذج متاحة في المزود المحدد')
    }

    // البحث عن نموذج مناسب للمهمة
    switch (taskType) {
      case 'code':
        const codeModel = models.find(m => 
          m.id.includes('code') || m.id.includes('deepseek') || m.name.includes('💻')
        )
        return codeModel?.id || models[0].id
      
      case 'creative':
        const creativeModel = models.find(m => 
          m.id.includes('literary') || m.id.includes('creative') || m.name.includes('📚')
        )
        return creativeModel?.id || models[0].id
      
      default:
        // للمحادثة العامة، اختيار أول نموذج متاح
        return models[0].id
    }
  }

  // تحديث إعدادات المزود
  async updateProviderSettings(providerId: string, settings: any): Promise<boolean> {
    try {
      switch (providerId) {
        case 'openrouter':
          if (settings.apiKey && this.openRouterAPI) {
            this.openRouterAPI.updateApiKey(settings.apiKey)
            localStorage.setItem('openrouter-api-key', settings.apiKey)
          }
          break
        case 'msty':
          if (settings.baseURL) {
            this.mstyAPI.updateConnection(settings.baseURL, settings.apiKey)
          }
          break
      }
      
      // إعادة تهيئة المزود
      await this.initializeProviders()
      return true
    } catch (error) {
      console.error('❌ خطأ في تحديث إعدادات المزود:', error)
      return false
    }
  }

  // الحصول على إحصائيات الخدمة
  getServiceStats(): {
    totalProviders: number
    connectedProviders: number
    totalModels: number
    promptsStats: any
  } {
    const providers = this.getProviders()
    const connectedProviders = this.getConnectedProviders()
    const totalModels = this.getAllAvailableModels().length
    const promptsStats = promptsLibrary.getUsageStats()

    return {
      totalProviders: providers.length,
      connectedProviders: connectedProviders.length,
      totalModels,
      promptsStats
    }
  }

  // إعادة تهيئة جميع الخدمات
  async reinitialize(): Promise<void> {
    console.log('🔄 إعادة تهيئة جميع الخدمات...')
    this.providers.clear()
    this.isInitialized = false
    await this.initializeProviders()
  }
}

// إنشاء مثيل مشترك
export const advancedAI = new AdvancedAIService()
