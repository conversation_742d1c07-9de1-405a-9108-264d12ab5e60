import axios, { AxiosInstance } from 'axios'

export class OllamaAPI {
  private apiClient: AxiosInstance
  private baseURL: string
  private isAvailable = false

  constructor(baseURL: string = 'http://localhost:11434/v1') {
    this.baseURL = baseURL
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000
    })
  }

  // Check Ollama availability
  async checkAvailability(): Promise<boolean> {
    try {
      const response = await this.apiClient.get('/models', { timeout: 5000 })
      this.isAvailable = response.status === 200
      return this.isAvailable
    } catch (error) {
      this.isAvailable = false
      return false
    }
  }

  // Get available models
  async getModels(): Promise<any[]> {
    try {
      if (!this.isAvailable) {
        await this.checkAvailability()
      }

      if (!this.isAvailable) {
        return []
      }

      const response = await this.apiClient.get('/models')
      const models = response.data.models || []

      return models.map((model: any) => ({
        id: model.name,
        name: `🏠 ${model.name} (Ollama)`,
        description: model.description || `Ollama Model: ${model.name}`,
        provider: 'ollama',
        type: 'local',
        available: true
      }))
    } catch (error) {
      console.error('Error getting Ollama models:', error)
      return []
    }
  }

  // Send message to Ollama
  async sendMessage(message: string, model: string, conversationHistory: any[] = []): Promise<string> {
    try {
      if (!this.isAvailable) {
        throw new Error('Ollama is not available')
      }

      const messages = [
        {
          role: 'system',
          content: 'You are a helpful AI assistant. Answer questions in markdown format.'
        },
        ...conversationHistory,
        {
          role: 'user',
          content: message
        }
      ]

      const response = await this.apiClient.post('/chat/completions', {
        model: model,
        messages: messages,
        stream: false
      })

      if (response.data?.choices?.[0]?.message?.content) {
        return response.data.choices[0].message.content.trim()
      } else {
        throw new Error('Could not get a valid response from Ollama')
      }
    } catch (error) {
      console.error('Error sending message to Ollama:', error)
      throw new Error(`Ollama Error: ${(error as any).message}`)
    }
  }

  // Check if Ollama is available
  get available(): boolean {
    return this.isAvailable
  }
}