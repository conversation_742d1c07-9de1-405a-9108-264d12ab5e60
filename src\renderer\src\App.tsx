import React, { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import Sidebar from './components/Sidebar'
import SettingsPanel from './components/SettingsPanel'
import { Conversation } from './types'
import { advancedAI } from '../services/AdvancedAIService'
import { promptsLibrary } from '../services/PromptsLibrary'
import './App.css'

interface AppState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  isLoading: boolean
  showSettings: boolean
  settings: any
  availableModels: any[]
  serviceStats: any
  connectedProviders: any[]
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    conversations: [],
    currentConversation: null,
    isLoading: false,
    showSettings: false,
    settings: {
      theme: 'dark',
      language: 'ar',
      preferredProvider: 'auto',
      useEmotionalAI: true,
      autoSwitchModels: true
    },
    availableModels: [],
    serviceStats: null,
    connectedProviders: []
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    console.log('🚀 بدء تهيئة التطبيق المتقدم...')

    // تطبيق الثيم الافتراضي فوراً
    document.documentElement.setAttribute('data-theme', 'dark')

    try {
      // تهيئة الخدمة المتقدمة
      await advancedAI.reinitialize()

      // تحديث النماذج والمزودين المتاحين
      const availableModels = advancedAI.getAllAvailableModels()
      const connectedProviders = advancedAI.getConnectedProviders()
      const serviceStats = advancedAI.getServiceStats()

      setState(prev => ({
        ...prev,
        availableModels,
        connectedProviders,
        serviceStats
      }))

      console.log('✅ تم تهيئة التطبيق بنجاح!')
      console.log(`📊 الإحصائيات: ${connectedProviders.length} مزود متصل، ${availableModels.length} نموذج متاح`)

    } catch (error) {
      console.error('❌ خطأ في تهيئة التطبيق:', error)
      // التطبيق سيعمل بالوضع الاحتياطي
    }
  }

  // إنشاء محادثة جديدة (مبسط)
  const createNewConversation = async (title?: string) => {
    console.log('🆕 إنشاء محادثة جديدة...')

    const newConversation = {
      id: Date.now().toString(),
      title: title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`,
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    setState(prev => ({
      ...prev,
      conversations: [newConversation, ...prev.conversations],
      currentConversation: newConversation
    }))

    console.log('✅ تم إنشاء محادثة جديدة:', newConversation.title)
  }

  // تحديد المحادثة الحالية (مبسط)
  const selectConversation = async (conversationId: string) => {
    const conversation = state.conversations.find(c => c.id === conversationId)
    if (conversation) {
      setState(prev => ({ ...prev, currentConversation: conversation }))
      console.log('✅ تم تحديد المحادثة:', conversation.title)
    }
  }

  // حذف محادثة (مبسط)
  const deleteConversation = async (conversationId: string) => {
    setState(prev => {
      const updatedConversations = prev.conversations.filter(c => c.id !== conversationId)
      const newCurrent = prev.currentConversation?.id === conversationId
        ? updatedConversations[0] || null
        : prev.currentConversation

      return {
        ...prev,
        conversations: updatedConversations,
        currentConversation: newCurrent
      }
    })
    console.log('🗑️ تم حذف المحادثة')
  }

  // إرسال رسالة مع الخدمة المتقدمة
  const sendMessage = async (message: string, model: string, promptTemplate?: string) => {
    console.log('📤 إرسال رسالة متقدمة:', message)

    // إنشاء محادثة جديدة إذا لم تكن موجودة
    if (!state.currentConversation) {
      await createNewConversation()
    }

    try {
      // إنشاء رسالة المستخدم
      const userMessage = {
        id: Date.now().toString(),
        role: 'user' as const,
        content: message,
        timestamp: new Date().toISOString()
      }

      // تحديث المحادثة بالرسالة الجديدة فوراً
      if (state.currentConversation) {
        const tempConversation = {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, userMessage],
          updatedAt: new Date().toISOString()
        }

        setState(prev => ({
          ...prev,
          currentConversation: tempConversation,
          conversations: prev.conversations.map(conv =>
            conv.id === tempConversation.id ? tempConversation : conv
          )
        }))
      }

      // إرسال الرسالة للخدمة المتقدمة
      const result = await advancedAI.sendMessage(message, {
        model: model || undefined,
        usePromptTemplate: promptTemplate,
        conversationId: state.currentConversation?.id,
        settings: {
          preferredProvider: state.settings.preferredProvider,
          useEmotionalAI: state.settings.useEmotionalAI,
          autoSwitchModels: state.settings.autoSwitchModels
        }
      })

      if (result.success && result.response) {
        // إنشاء رد البوت
        const botMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant' as const,
          content: result.response,
          timestamp: new Date().toISOString(),
          metadata: {
            provider: result.provider,
            model: result.model,
            usage: result.usage,
            emotionalAnalysis: result.emotionalAnalysis
          }
        }

        // تحديث المحادثة بالرد
        if (state.currentConversation) {
          const updatedConversation = {
            ...state.currentConversation,
            messages: [...state.currentConversation.messages.slice(0, -1), userMessage, botMessage],
            updatedAt: new Date().toISOString()
          }

          setState(prev => ({
            ...prev,
            currentConversation: updatedConversation,
            conversations: prev.conversations.map(conv =>
              conv.id === updatedConversation.id ? updatedConversation : conv
            )
          }))
        }

        console.log(`✅ تم الرد بنجاح من ${result.provider} باستخدام ${result.model}`)
        return { success: true, provider: result.provider, model: result.model }
      } else {
        throw new Error(result.error || 'فشل في الحصول على رد من الذكاء الاصطناعي')
      }

    } catch (error) {
      console.error('❌ خطأ في إرسال الرسالة:', error)

      // إضافة رسالة خطأ للمحادثة
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant' as const,
        content: `عذراً، حدث خطأ: ${error.message}\n\nيرجى التحقق من:\n- اتصال الإنترنت\n- إعدادات الخدمة\n- توفر النماذج المحلية`,
        timestamp: new Date().toISOString(),
        isError: true
      }

      if (state.currentConversation) {
        const updatedConversation = {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, errorMessage],
          updatedAt: new Date().toISOString()
        }

        setState(prev => ({
          ...prev,
          currentConversation: updatedConversation,
          conversations: prev.conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedConversation : conv
          )
        }))
      }

      return { success: false, error: error.message }
    }
  }

  // حفظ الإعدادات (مبسط)
  const saveSettings = async (newSettings: any) => {
    setState(prev => ({ ...prev, settings: { ...prev.settings, ...newSettings } }))

    // تطبيق السمة الجديدة
    if (newSettings.theme) {
      document.documentElement.setAttribute('data-theme', newSettings.theme)
    }

    console.log('⚙️ تم حفظ الإعدادات:', newSettings)
    return { success: true }
  }

  // تصدير محادثة (مبسط)
  const exportConversation = async (conversationId: string, format: string) => {
    console.log('📤 تصدير المحادثة:', conversationId, format)
    // سيتم تطبيق التصدير الحقيقي لاحقاً
    return { success: true, message: 'سيتم تطبيق التصدير قريباً' }
  }

  // 🔥 إزالة شاشة التحميل نهائياً - الدخول مباشرة للواجهة الرئيسية

  return (
    <div className="app">
      <div className="app-container">
        {/* الشريط الجانبي */}
        <Sidebar
          conversations={state.conversations}
          currentConversation={state.currentConversation}
          onSelectConversation={selectConversation}
          onCreateConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onExportConversation={exportConversation}
          onShowSettings={() => setState(prev => ({ ...prev, showSettings: true }))}
        />

        {/* واجهة الدردشة الرئيسية */}
        <div className="main-content">
          <ChatInterface
            conversation={state.currentConversation}
            availableModels={state.availableModels}
            settings={state.settings}
            onSendMessage={sendMessage}
            onCreateConversation={createNewConversation}
          />
        </div>

        {/* لوحة الإعدادات */}
        {state.showSettings && (
          <SettingsPanel
            settings={state.settings}
            availableModels={state.availableModels}
            onSaveSettings={saveSettings}
            onClose={() => setState(prev => ({ ...prev, showSettings: false }))}
          />
        )}
      </div>
    </div>
  )
}

export default App
