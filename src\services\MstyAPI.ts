import axios, { AxiosInstance } from 'axios'
import { emotionalAI } from './EmotionalAI'

/**
 * خدمة الاتصال مع Msty - محسنة ومطورة
 * Msty هو تطبيق محلي متقدم للذكاء الاصطناعي مع دعم للنماذج المحلية والسحابية
 * يوفر واجهة API متوافقة مع OpenAI للتكامل السهل
 */
export class MstyAPI {
  private apiClient: AxiosInstance
  private baseURL = 'http://localhost:10000/v1' // منفذ Msty الافتراضي
  private isAvailable = false
  private currentModel: string | null = null
  private availableModels: any[] = []
  private connectionRetries = 0
  private maxRetries = 5

  constructor() {
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer msty-local-key', // مفتاح محلي افتراضي
        'User-Agent': 'AI-Chat-Bot/1.0',
        'X-Client': 'ai-chat-bot'
      },
      timeout: 30000
    })

    // إعداد interceptors للتعامل مع الأخطاء
    this.setupInterceptors()
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        console.log(`🔄 إرسال طلب إلى Msty: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('❌ خطأ في إعداد الطلب:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`✅ استجابة ناجحة من Msty: ${response.status}`)
        return response
      },
      (error) => {
        console.error('❌ خطأ في الاستجابة من Msty:', error.message)
        return Promise.reject(error)
      }
    )
  }

  // فحص توفر Msty مع دعم متقدم للمنافذ والإعدادات
  async checkAvailability(): Promise<boolean> {
    const defaultPorts = [
      10000, // منفذ Msty الافتراضي
      8080,  // منفذ بديل شائع
      3000,  // منفذ تطوير
      5000,  // منفذ Flask
      11434, // منفذ Ollama
      1234,  // منفذ LM Studio
      8000,  // منفذ بديل
      9000   // منفذ إضافي
    ];

    console.log('🔍 بحث عن خدمة Msty على المنافذ المختلفة...')

    for (let retry = 0; retry < this.maxRetries; retry++) {
      for (const port of defaultPorts) {
        try {
          console.log(`🔌 فحص المنفذ ${port}...`)

          const client = axios.create({
            baseURL: `http://localhost:${port}`,
            timeout: 5000,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer msty-local-key',
              'User-Agent': 'AI-Chat-Bot/1.0'
            }
          });

          // محاولة الوصول لنقاط API مختلفة
          const endpoints = ['/v1/models', '/models', '/api/models', '/health', '/status'];

          for (const endpoint of endpoints) {
            try {
              const response = await client.get(endpoint);
              if (response.status === 200) {
                // تحديد نوع الخدمة بناءً على الاستجابة
                const serviceType = this.detectServiceType(response.data, port);

                this.baseURL = `http://localhost:${port}${endpoint.includes('/v1') ? '' : '/v1'}`;
                this.apiClient.defaults.baseURL = this.baseURL;
                this.isAvailable = true;
                this.connectionRetries = 0;

                console.log(`✅ تم الاتصال بـ ${serviceType} على المنفذ ${port}`);
                console.log(`🔗 عنوان API: ${this.baseURL}`);

                // تحديث قائمة النماذج المتاحة
                await this.refreshModels();

                return true;
              }
            } catch (endpointError) {
              // تجاهل أخطاء نقاط API الفردية والمتابعة
              continue;
            }
          }
        } catch (error) {
          // تجاهل أخطاء المنافذ والمتابعة
          continue;
        }
      }

      if (retry < this.maxRetries - 1) {
        this.connectionRetries++;
        console.log(`⏳ محاولة إعادة الاتصال ${retry + 1}/${this.maxRetries}...`);
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    this.isAvailable = false;
    this.connectionRetries = this.maxRetries;
    console.log('❌ تعذر الاتصال بأي خدمة ذكاء اصطناعي محلية');
    console.log('💡 تأكد من تشغيل إحدى الخدمات التالية:');
    console.log('   - Msty (المنفذ 10000)');
    console.log('   - LM Studio (المنفذ 1234)');
    console.log('   - Ollama (المنفذ 11434)');

    return false;
  }

  // تحديد نوع الخدمة بناءً على الاستجابة
  private detectServiceType(responseData: any, port: number): string {
    if (port === 10000) return 'Msty';
    if (port === 1234) return 'LM Studio';
    if (port === 11434) return 'Ollama';

    // تحليل محتوى الاستجابة
    if (responseData?.object === 'list' && responseData?.data) {
      return 'OpenAI-Compatible API';
    }

    return 'خدمة ذكاء اصطناعي محلية';
  }

  // تحديث قائمة النماذج المتاحة
  private async refreshModels(): Promise<void> {
    try {
      const response = await this.apiClient.get('/models');
      const models = response.data.data || response.data.models || response.data || [];

      this.availableModels = models.map((model: any) => ({
        id: model.id || model.name || model.model,
        name: this.formatModelName(model.id || model.name || model.model),
        description: model.description || `نموذج محلي: ${model.id || model.name}`,
        provider: 'msty',
        type: 'local',
        available: true,
        size: model.size || 'غير محدد',
        modified: model.modified_at || model.modified || null,
        details: model.details || null
      }));

      console.log(`📋 تم تحديث قائمة النماذج: ${this.availableModels.length} نموذج متاح`);

      // تعيين النموذج الافتراضي إذا لم يكن محدداً
      if (!this.currentModel && this.availableModels.length > 0) {
        this.currentModel = this.availableModels[0].id;
        console.log(`🎯 تم تعيين النموذج الافتراضي: ${this.currentModel}`);
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث النماذج:', error);
      this.availableModels = [];
    }
  }

  // تنسيق اسم النموذج لعرض أفضل
  private formatModelName(modelId: string): string {
    if (!modelId) return 'نموذج غير معروف';

    // إزالة البادئات الشائعة وتحسين العرض
    const cleanName = modelId
      .replace(/^(ollama\/|huggingface\/|local\/)/i, '')
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());

    // إضافة رموز تعبيرية حسب نوع النموذج
    if (modelId.includes('llama')) return `🦙 ${cleanName}`;
    if (modelId.includes('mistral')) return `🌪️ ${cleanName}`;
    if (modelId.includes('gemma')) return `💎 ${cleanName}`;
    if (modelId.includes('qwen')) return `🧠 ${cleanName}`;
    if (modelId.includes('deepseek')) return `🔍 ${cleanName}`;
    if (modelId.includes('code')) return `💻 ${cleanName}`;

    return `🤖 ${cleanName}`;
  }

  // الحصول على النماذج المتاحة مع معلومات محسنة
  async getModels(): Promise<any[]> {
    try {
      if (!this.isAvailable) {
        const connected = await this.checkAvailability();
        if (!connected) {
          console.log('⚠️ لا توجد خدمة محلية متاحة، إرجاع نماذج افتراضية');
          return this.getDefaultLocalModels();
        }
      }

      // إرجاع النماذج المحدثة
      if (this.availableModels.length === 0) {
        await this.refreshModels();
      }

      return this.availableModels.length > 0 ? this.availableModels : this.getDefaultLocalModels();
    } catch (error) {
      console.error('❌ خطأ في جلب نماذج Msty:', error);
      return this.getDefaultLocalModels();
    }
  }

  // نماذج افتراضية في حالة عدم توفر خدمة محلية
  private getDefaultLocalModels(): any[] {
    return [
      {
        id: 'local-chat-basic',
        name: '💬 محادثة أساسية',
        description: 'نموذج محلي للمحادثة العامة - يعمل بدون اتصال',
        provider: 'local',
        type: 'fallback',
        available: true,
        size: 'صغير',
        modified: null,
        details: 'نموذج احتياطي مدمج'
      },
      {
        id: 'local-assistant',
        name: '🤖 المساعد الذكي',
        description: 'مساعد ذكي محلي للمهام العامة',
        provider: 'local',
        type: 'fallback',
        available: true,
        size: 'متوسط',
        modified: null,
        details: 'نموذج احتياطي مدمج'
      }
    ];
  }

  // إرسال رسالة للدردشة مع تحسينات متقدمة
  async sendMessage(messages: any[], model: string): Promise<any> {
    try {
      if (!this.isAvailable) {
        // محاولة إعادة الاتصال
        const reconnected = await this.checkAvailability();
        if (!reconnected) {
          // استخدام النموذج الاحتياطي المحلي
          return await this.handleFallbackResponse(messages, model);
        }
      }

      // تحليل المشاعر في آخر رسالة
      const lastMessage = messages[messages.length - 1];
      const userMessage = lastMessage?.content || '';

      // تحليل المشاعر وتحسين الاستجابة
      const emotionalAnalysis = emotionalAI.getFullEmotionalAnalysis(userMessage, model);

      // إعداد الرسالة النظامية المحسنة
      const systemMessage = this.buildEnhancedSystemMessage(emotionalAnalysis, model);

      // تنظيف وتحسين الرسائل
      const enhancedMessages = this.prepareMessages(messages, systemMessage);

      // إعداد معاملات النموذج المحسنة
      const modelParams = this.getOptimalModelParams(model, userMessage);

      console.log(`📤 إرسال رسالة إلى النموذج: ${model}`);
      console.log(`🎯 معاملات النموذج:`, modelParams);

      const response = await this.apiClient.post('/chat/completions', {
        model: model,
        messages: enhancedMessages,
        ...modelParams,
        stream: false
      });

      if (response.data?.choices?.[0]?.message?.content) {
        const aiResponse = response.data.choices[0].message.content;

        // تسجيل معلومات الاستخدام
        if (response.data.usage) {
          console.log(`📊 استخدام النموذج:`, {
            prompt_tokens: response.data.usage.prompt_tokens,
            completion_tokens: response.data.usage.completion_tokens,
            total_tokens: response.data.usage.total_tokens
          });
        }

        return {
          success: true,
          message: aiResponse,
          usage: response.data.usage,
          model_used: model,
          emotional_analysis: emotionalAnalysis.emotionalState.confidence > 0.7 ? {
            emotion: emotionalAnalysis.emotionalState.primary,
            intensity: emotionalAnalysis.emotionalState._intensity,
            confidence: emotionalAnalysis.emotionalState.confidence
          } : null
        };
      } else {
        throw new Error('لم يتم الحصول على رد صحيح من النموذج');
      }
    } catch (error) {
      console.error('❌ خطأ في إرسال رسالة لـ Msty:', error);

      // محاولة استخدام النموذج الاحتياطي
      if (this.isAvailable) {
        return await this.handleFallbackResponse(messages, model);
      }

      return {
        success: false,
        error: (error as any).message || 'خطأ في الاتصال مع Msty',
        fallback_attempted: true
      };
    }
  }

  // بناء رسالة نظامية محسنة
  private buildEnhancedSystemMessage(emotionalAnalysis: any, model: string): string {
    let baseMessage = emotionalAnalysis.systemMessage;

    // إضافة تعليمات خاصة بالنموذج
    if (model.includes('code')) {
      baseMessage += '\n\nتركز على البرمجة وكتابة الكود بجودة عالية مع شرح واضح.';
    } else if (model.includes('creative') || model.includes('literary')) {
      baseMessage += '\n\nتركز على الإبداع والكتابة الأدبية بأسلوب جميل ومؤثر.';
    }

    // إضافة تعليمات اللغة العربية
    baseMessage += '\n\nاستخدم اللغة العربية الفصحى مع مراعاة الوضوح والبساطة. كن مفيداً ومهذباً دائماً.';

    return baseMessage;
  }

  // تحضير الرسائل مع التنظيف والتحسين
  private prepareMessages(messages: any[], systemMessage: string): any[] {
    // إزالة الرسائل النظامية القديمة
    const cleanMessages = messages.filter(msg => msg.role !== 'system');

    // إضافة الرسالة النظامية الجديدة في البداية
    return [
      { role: 'system', content: systemMessage },
      ...cleanMessages.slice(-10) // الاحتفاظ بآخر 10 رسائل فقط لتوفير الذاكرة
    ];
  }

  // الحصول على معاملات النموذج المثلى
  private getOptimalModelParams(model: string, userMessage: string): any {
    const baseParams = {
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.9,
      frequency_penalty: 0.1,
      presence_penalty: 0.1
    };

    // تخصيص المعاملات حسب نوع النموذج
    if (model.includes('code')) {
      return {
        ...baseParams,
        temperature: 0.3, // أقل إبداعاً للكود
        max_tokens: 3000
      };
    } else if (model.includes('creative') || model.includes('literary')) {
      return {
        ...baseParams,
        temperature: 0.9, // أكثر إبداعاً للكتابة
        max_tokens: 4000
      };
    } else if (userMessage.length > 500) {
      return {
        ...baseParams,
        max_tokens: 3000 // رد أطول للرسائل الطويلة
      };
    }

    return baseParams;
  }

  // معالجة الاستجابة الاحتياطية
  private async handleFallbackResponse(messages: any[], model: string): Promise<any> {
    console.log('🔄 استخدام النموذج الاحتياطي المحلي...');

    const lastMessage = messages[messages.length - 1];
    const userMessage = lastMessage?.content || '';

    // استجابات ذكية بناءً على محتوى الرسالة
    const response = this.generateIntelligentFallback(userMessage, model);

    return {
      success: true,
      message: response,
      usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
      model_used: 'local-fallback',
      is_fallback: true
    };
  }

  // توليد استجابة احتياطية ذكية
  private generateIntelligentFallback(userMessage: string, model: string): string {
    const message = userMessage.toLowerCase();

    // استجابات للبرمجة
    if (message.includes('كود') || message.includes('برمجة') || message.includes('code')) {
      return `أعتذر، الخدمة المحلية غير متاحة حالياً. لكن يمكنني مساعدتك في البرمجة:

للحصول على مساعدة في البرمجة، تأكد من:
1. تشغيل Msty أو LM Studio
2. تحميل نموذج مناسب للبرمجة مثل CodeLlama
3. التأكد من الاتصال بالمنفذ الصحيح

في غضون ذلك، يمكنك استخدام الخدمات السحابية من خلال الإعدادات.`;
    }

    // استجابات للكتابة الإبداعية
    if (message.includes('قصة') || message.includes('شعر') || message.includes('كتابة')) {
      return `أعتذر، الخدمة المحلية غير متاحة حالياً. لكن يمكنني مساعدتك في الكتابة الإبداعية:

للحصول على مساعدة في الكتابة:
1. تأكد من تشغيل Msty مع نموذج أدبي
2. أو استخدم الخدمات السحابية للحصول على نماذج متخصصة
3. جرب النماذج الأدبية المتاحة في الإعدادات

سأكون سعيداً لمساعدتك عند توفر الخدمة!`;
    }

    // استجابة عامة
    return `مرحباً! أعتذر، الخدمة المحلية غير متاحة حالياً.

للحصول على أفضل تجربة:
🔧 تأكد من تشغيل إحدى الخدمات التالية:
   • Msty (المنفذ 10000)
   • LM Studio (المنفذ 1234)
   • Ollama (المنفذ 11434)

🌐 أو استخدم الخدمات السحابية من خلال:
   • إعداد مفتاح OpenRouter في الإعدادات
   • اختيار نموذج سحابي من القائمة

سأكون جاهزاً لمساعدتك بمجرد توفر إحدى الخدمات! 😊`;
  }

  // إرسال رسالة مع تدفق (streaming)
  async sendMessageStream(messages: any[], model: string, onChunk: (chunk: string) => void): Promise<any> {
    try {
      if (!this.isAvailable) {
        throw new Error('Msty غير متاح')
      }

      // تحليل المشاعر في آخر رسالة
      const lastMessage = messages[messages.length - 1]
      const userMessage = lastMessage?.content || ''

      // تحليل المشاعر وتحسين الاستجابة
      const emotionalAnalysis = emotionalAI.getFullEmotionalAnalysis(userMessage, model)

      // تحديث الرسالة النظامية مع التحليل العاطفي
      const systemMessage = emotionalAnalysis.systemMessage
      const enhancedMessages = [
        { role: 'system', content: systemMessage },
        ...messages.filter(msg => msg.role !== 'system')
      ]

      const response = await this.apiClient.post('/chat/completions', {
        model: model,
        messages: enhancedMessages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: true
      }, {
        responseType: 'stream'
      })

      let fullMessage = ''

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)

            if (data === '[DONE]') {
              return
            }

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content

              if (content) {
                fullMessage += content
                onChunk(content)
              }
            } catch (parseError) {
              // تجاهل أخطاء التحليل
            }
          }
        }
      })

      return new Promise((resolve) => {
        response.data.on('end', () => {
          resolve({
            success: true,
            message: fullMessage
          })
        })

        response.data.on('error', (error: any) => {
                  resolve({
                    success: false,
                    error: (error as any).message
                  })
        })
      })
    } catch (error) {
      console.error('خطأ في إرسال رسالة متدفقة لـ Msty:', error)
      return {
        success: false,
        error: (error as any).message || 'خطأ في الاتصال مع Msty'
      }
    }
  }

  // فحص حالة النموذج
  async getModelInfo(modelId: string): Promise<any> {
    try {
      const response = await this.apiClient.get(`/models/${modelId}`)
      return {
        success: true,
        model: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: (error as any).message
      }
    }
  }

  // تحديث إعدادات الاتصال
  updateConnection(baseURL: string, apiKey?: string): void {
    this.baseURL = baseURL
    this.apiClient.defaults.baseURL = baseURL

    if (apiKey) {
      this.apiClient.defaults.headers['Authorization'] = `Bearer ${apiKey}`
    }
  }

  // الحصول على معلومات الخادم
  async getServerInfo(): Promise<any> {
    try {
      const response = await this.apiClient.get('/health')
      return {
        success: true,
        info: response.data,
        baseURL: this.baseURL,
        available: this.isAvailable
      }
    } catch (error) {
      return {
        success: false,
        error: (error as any).message,
        baseURL: this.baseURL,
        available: false
      }
    }
  }
}

// إنشاء مثيل مشترك
export const mstyAPI = new MstyAPI()
